"""
Data models for the Component Monitoring System.

This module contains Pydantic models for API responses related to
component health, performance, errors, and debugging.
"""

from pydantic import BaseModel
from typing import Dict, List, Optional, Literal
from datetime import datetime


class ComponentHealth(BaseModel):
    """Health status for a system component."""
    status: Literal["healthy", "warning", "error"]
    health_score: int  # 0-100
    recent_errors: int
    last_check: str  # ISO datetime string
    uptime_percentage: float
    response_time_ms: float


class ComponentPerformance(BaseModel):
    """Performance metrics for a system component."""
    avg_time_ms: float
    success_rate: float
    error_rate: float
    total_requests: int
    requests_per_minute: int
    p95_response_time: float
    p99_response_time: float


class ComponentError(BaseModel):
    """Individual error record for a component."""
    error_id: str
    component: str
    error_type: str
    message: str
    timestamp: str
    frequency: int
    severity: Literal["low", "medium", "high", "critical"]
    context: Optional[Dict] = None


class ComponentErrorResponse(BaseModel):
    """Error summary response for a component."""
    component: str
    total_errors: int
    recent_errors: List[ComponentError]
    common_patterns: List[str]
    error_rate: float


class RecoveryStats(BaseModel):
    """Recovery statistics for a component."""
    recovery_attempts: int
    recovery_successes: int
    recovery_success_rate: float
    avg_recovery_time_ms: float
    last_recovery_attempt: Optional[str] = None


class FallbackStats(BaseModel):
    """Fallback system statistics."""
    total_fallbacks: int
    fallback_rate: float
    fallback_success_rate: float
    common_fallback_triggers: List[str]


class ComponentDebugRequest(BaseModel):
    """Request model for component debugging."""
    component: str
    message: str
    tenant_id: Optional[str] = None
    debug_level: Literal["basic", "detailed", "verbose"] = "basic"


class ComponentDebugResponse(BaseModel):
    """Response model for component debugging."""
    component: str
    status: Literal["success", "error"]
    processing_time_ms: float
    debug_info: Dict
    errors: Optional[List[str]] = []
    warnings: Optional[List[str]] = []
