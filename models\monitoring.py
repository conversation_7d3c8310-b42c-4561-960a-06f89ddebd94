"""
Data models for the Component Performance Monitoring System.

This module contains Pydantic models for API responses related to
component performance metrics.
"""

from pydantic import BaseModel
from typing import Dict, List, Optional, Literal
from datetime import datetime


class ComponentPerformance(BaseModel):
    """Performance metrics for a system component."""
    avg_time_ms: float
    success_rate: float
    error_rate: float
    total_requests: int
    requests_per_minute: int
    p95_response_time: float
    p99_response_time: float
