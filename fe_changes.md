# Frontend Changes for Ultra-Modular Intent Parsing

## 🎯 **Overview**
This document outlines the frontend changes needed in the briskk-ai-website repo to integrate with the new ultra-modular intent parsing architecture.

## 🔧 **Critical Integration Updates**

### **1. Error Tracking Integration**
Update error handling to use new modular error system:

```javascript
// File: src/components/DebugPanel.jsx
// OLD approach:
const errorData = await fetch('/api/intent/errors');

// NEW approach - component-specific error tracking:
const ruleErrors = await fetch('/api/intent/errors/rule_engine');
const cacheErrors = await fetch('/api/intent/errors/cache');
const llmErrors = await fetch('/api/intent/errors/llm');
const clarifierErrors = await fetch('/api/intent/errors/clarifier');
```

### **2. Performance Monitoring Updates**
Update performance displays for component-level metrics:

```javascript
// File: src/components/PerformanceMetrics.jsx
// NEW component-specific performance tracking:
const performanceData = {
  rule_engine: { avg_time: '2.5ms', success_rate: '99.2%' },
  cache: { avg_time: '1.1ms', hit_rate: '85.3%' },
  llm: { avg_time: '1250ms', success_rate: '97.8%' },
  clarifier: { avg_time: '45ms', trigger_rate: '12.5%' }
};
```

### **3. Debug Panel Enhancements**
Add component-specific debugging capabilities:

```javascript
// File: src/components/DebugPanel.jsx
const debugComponents = [
  'rule_engine',
  'cache', 
  'llm',
  'clarifier',
  'orchestration'
];

// Add component health indicators
const componentHealth = await fetch('/api/intent/health/components');
```

## 📊 **New API Endpoints to Integrate**

### **4. Component-Specific Routes**
Add these new API routes to your backend integration:

```javascript
// Component-specific error tracking
GET /api/intent/errors/rule_engine
GET /api/intent/errors/cache
GET /api/intent/errors/llm
GET /api/intent/errors/clarifier

// Component health checks
GET /api/intent/health/components
GET /api/intent/health/rule_engine
GET /api/intent/health/cache
GET /api/intent/health/llm
GET /api/intent/health/clarifier

// Component-specific performance
GET /api/intent/performance/components
GET /api/intent/performance/rule_engine
GET /api/intent/performance/cache
GET /api/intent/performance/llm
GET /api/intent/performance/clarifier

// Fallback and recovery tracking
GET /api/intent/fallback/stats
GET /api/intent/recovery/capabilities
```

## 🎨 **New UI Components to Create**

### **5. Component Health Dashboard**
Create new component health visualization:

```jsx
// File: src/components/ComponentHealthDashboard.jsx
const ComponentHealthDashboard = () => {
  const [componentHealth, setComponentHealth] = useState({});
  
  const healthColors = {
    healthy: 'green',
    warning: 'yellow', 
    error: 'red'
  };
  
  return (
    <div className="component-health-grid">
      {Object.entries(componentHealth).map(([component, health]) => (
        <div key={component} className={`health-card ${health.status}`}>
          <h3>{component}</h3>
          <div className="health-score">{health.health_score}/100</div>
          <div className="error-count">{health.recent_errors} recent errors</div>
        </div>
      ))}
    </div>
  );
};
```

### **6. Error Pattern Visualization**
Add error pattern tracking visualization:

```jsx
// File: src/components/ErrorPatternChart.jsx
const ErrorPatternChart = () => {
  const [errorPatterns, setErrorPatterns] = useState({});
  
  return (
    <div className="error-patterns">
      <h3>Error Patterns by Component</h3>
      {Object.entries(errorPatterns).map(([component, patterns]) => (
        <div key={component} className="pattern-section">
          <h4>{component}</h4>
          <ul>
            {patterns.common_patterns.map((pattern, idx) => (
              <li key={idx} className="pattern-item">{pattern}</li>
            ))}
          </ul>
        </div>
      ))}
    </div>
  );
};
```

### **7. Component-Specific Debug Tools**
Add component isolation debugging:

```jsx
// File: src/components/ComponentDebugger.jsx
const ComponentDebugger = () => {
  const [selectedComponent, setSelectedComponent] = useState('rule_engine');
  const [debugInfo, setDebugInfo] = useState({});
  
  const debugComponent = async (component, message) => {
    const response = await fetch('/api/intent/debug/component', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        component, 
        message, 
        tenant_id: 'debug_tenant' 
      })
    });
    
    const result = await response.json();
    setDebugInfo(result);
  };
  
  return (
    <div className="component-debugger">
      <select 
        value={selectedComponent} 
        onChange={(e) => setSelectedComponent(e.target.value)}
      >
        <option value="rule_engine">Rule Engine</option>
        <option value="cache">Cache</option>
        <option value="llm">LLM</option>
        <option value="clarifier">Clarifier</option>
      </select>
      
      <button onClick={() => debugComponent(selectedComponent, testMessage)}>
        Debug {selectedComponent}
      </button>
      
      <pre>{JSON.stringify(debugInfo, null, 2)}</pre>
    </div>
  );
};
```

## 📈 **Performance Monitoring Updates**

### **8. Component Performance Metrics**
Update performance monitoring for component-level metrics:

```jsx
// File: src/components/PerformanceMonitor.jsx
const PerformanceMonitor = () => {
  const [componentPerf, setComponentPerf] = useState({});
  
  useEffect(() => {
    const fetchComponentPerformance = async () => {
      const response = await fetch('/api/intent/performance/components');
      const data = await response.json();
      setComponentPerf(data);
    };
    
    fetchComponentPerformance();
    const interval = setInterval(fetchComponentPerformance, 5000);
    return () => clearInterval(interval);
  }, []);
  
  return (
    <div className="performance-grid">
      {Object.entries(componentPerf).map(([component, metrics]) => (
        <div key={component} className="perf-card">
          <h3>{component}</h3>
          <div className="metric">
            <span>Avg Time:</span>
            <span>{metrics.avg_time_ms}ms</span>
          </div>
          <div className="metric">
            <span>Success Rate:</span>
            <span>{metrics.success_rate}%</span>
          </div>
          <div className="metric">
            <span>Error Rate:</span>
            <span>{metrics.error_rate}%</span>
          </div>
        </div>
      ))}
    </div>
  );
};
```

## 🚨 **Error Recovery Tracking**

### **9. Recovery Status Dashboard**
Add recovery tracking visualization:

```jsx
// File: src/components/RecoveryDashboard.jsx
const RecoveryDashboard = () => {
  const [recoveryStats, setRecoveryStats] = useState({});
  
  return (
    <div className="recovery-dashboard">
      <h3>Error Recovery Status</h3>
      <div className="recovery-grid">
        {Object.entries(recoveryStats).map(([component, stats]) => (
          <div key={component} className="recovery-card">
            <h4>{component}</h4>
            <div className="recovery-rate">
              Recovery Rate: {stats.recovery_success_rate}%
            </div>
            <div className="attempts">
              Attempts: {stats.recovery_attempts}
            </div>
            <div className="successes">
              Successes: {stats.recovery_successes}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
```

## 🔧 **Configuration Updates**

### **10. Environment Variables**
Add these to your .env file:

```bash
# Component-specific debugging
REACT_APP_ENABLE_COMPONENT_DEBUG=true
REACT_APP_DEBUG_COMPONENTS=rule_engine,cache,llm,clarifier

# Enhanced error tracking
REACT_APP_ERROR_TRACKING_LEVEL=component
REACT_APP_SHOW_ERROR_PATTERNS=true

# Performance monitoring
REACT_APP_COMPONENT_PERFORMANCE_MONITORING=true
REACT_APP_PERFORMANCE_UPDATE_INTERVAL=5000
```

## 📱 **Mobile/Responsive Updates**

### **11. Mobile Component Health View**
Add mobile-friendly component health view:

```jsx
// File: src/components/mobile/ComponentHealthMobile.jsx
const ComponentHealthMobile = () => {
  return (
    <div className="mobile-health-view">
      <div className="health-summary">
        <div className="overall-health">System Health: 95%</div>
        <div className="component-count">4/4 Components Healthy</div>
      </div>
      
      <div className="component-list">
        {components.map(component => (
          <div key={component.name} className="mobile-component-card">
            <div className="component-name">{component.name}</div>
            <div className={`health-indicator ${component.status}`}>
              {component.health_score}/100
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
```

## 🎯 **Implementation Priority**

### **High Priority (Implement First)**
1. **API Endpoint Updates** - Add new component-specific routes
2. **Error Tracking Integration** - Update error handling to use modular system
3. **Component Health Dashboard** - Add component health visualization

### **Medium Priority**
4. **Performance Monitoring Updates** - Component-level performance metrics
5. **Debug Panel Enhancements** - Component-specific debugging
6. **Error Pattern Visualization** - Pattern tracking charts

### **Low Priority (Nice to Have)**
7. **Recovery Status Dashboard** - Recovery tracking visualization
8. **Mobile Component Views** - Mobile-friendly health views
9. **Component Debugger** - Advanced debugging tools

## 📋 **Testing Checklist**

After implementing these changes, test:

- [ ] Component health indicators display correctly
- [ ] Error tracking shows component-specific errors
- [ ] Performance metrics update in real-time
- [ ] Debug tools work for each component
- [ ] Mobile views are responsive
- [ ] API endpoints return expected data
- [ ] Error patterns are visualized correctly
- [ ] Recovery tracking functions properly

## 🚀 **Expected Benefits**

After implementing these changes, you'll have:

- **Pinpoint Error Tracking**: See exactly which component (rule, cache, LLM, clarifier) has issues
- **Component Health Monitoring**: Real-time health status for each component
- **Enhanced Debugging**: Debug individual components in isolation
- **Performance Insights**: Component-level performance metrics
- **Recovery Tracking**: Monitor error recovery attempts and success rates
- **Better User Experience**: More precise error information and faster debugging
