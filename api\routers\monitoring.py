"""
FastAPI routes for Component Performance Monitoring System.

This module provides REST API endpoints for monitoring system component
performance metrics.
"""

from fastapi import APIRouter, HTTPException, Header, Request
from typing import Optional
from services.monitoring_service import MonitoringService
from models.monitoring import *

router = APIRouter(prefix="/api/intent", tags=["monitoring"])


def get_monitoring_service(request: Request) -> MonitoringService:
    """Get monitoring service with actual component references."""
    return MonitoringService(
        orchestrator=getattr(request.app.state, 'sales_agent_handler', None),
        intent_parser=getattr(request.app.state, 'intent_parser', None),
        cache_manager=None  # Will be enhanced later with actual cache manager
    )

# Performance Monitoring Endpoints
@router.get("/performance/components")
async def get_components_performance(
    request: Request,
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
):
    """Get performance metrics for all components."""
    try:
        monitoring_service = get_monitoring_service(request)
        performance_data = monitoring_service.get_component_performance()
        return performance_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get component performance: {str(e)}")

@router.get("/performance/{component}")
async def get_component_performance(
    component: str,
    request: Request,
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
):
    """Get performance metrics for a specific component."""
    try:
        if component not in ["rule_engine", "cache", "llm", "clarifier"]:
            raise HTTPException(status_code=404, detail=f"Component '{component}' not found")

        monitoring_service = get_monitoring_service(request)
        performance_data = monitoring_service.get_component_performance(component)
        return performance_data
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get component performance: {str(e)}")
