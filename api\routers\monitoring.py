"""
FastAPI routes for Component Monitoring System.

This module provides REST API endpoints for monitoring system components
including health checks, performance metrics, error tracking, and debugging.
"""

from fastapi import APIRouter, HTTPException, Header, Request
from typing import Optional
from services.monitoring_service import MonitoringService
from models.monitoring import *

router = APIRouter(prefix="/api/intent", tags=["monitoring"])


def get_monitoring_service(request: Request) -> MonitoringService:
    """Get monitoring service with actual component references."""
    return MonitoringService(
        orchestrator=getattr(request.app.state, 'sales_agent_handler', None),
        intent_parser=getattr(request.app.state, 'intent_parser', None),
        cache_manager=None  # Will be enhanced later with actual cache manager
    )

# Component Health Endpoints
@router.get("/health/components")
async def get_components_health(
    request: Request,
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
):
    """Get health status for all components."""
    try:
        monitoring_service = get_monitoring_service(request)
        health_data = monitoring_service.get_component_health()
        return health_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get component health: {str(e)}")

@router.get("/health/{component}")
async def get_component_health(
    component: str,
    request: Request,
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
):
    """Get health status for a specific component."""
    try:
        if component not in ["rule_engine", "cache", "llm", "clarifier"]:
            raise HTTPException(status_code=404, detail=f"Component '{component}' not found")

        monitoring_service = get_monitoring_service(request)
        health_data = monitoring_service.get_component_health(component)
        return health_data
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get component health: {str(e)}")

# Performance Monitoring Endpoints
@router.get("/performance/components")
async def get_components_performance(
    request: Request,
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
):
    """Get performance metrics for all components."""
    try:
        monitoring_service = get_monitoring_service(request)
        performance_data = monitoring_service.get_component_performance()
        return performance_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get component performance: {str(e)}")

@router.get("/performance/{component}")
async def get_component_performance(
    component: str,
    request: Request,
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
):
    """Get performance metrics for a specific component."""
    try:
        if component not in ["rule_engine", "cache", "llm", "clarifier"]:
            raise HTTPException(status_code=404, detail=f"Component '{component}' not found")

        monitoring_service = get_monitoring_service(request)
        performance_data = monitoring_service.get_component_performance(component)
        return performance_data
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get component performance: {str(e)}")

# Error Tracking Endpoints
@router.get("/errors/{component}")
async def get_component_errors(
    component: str,
    request: Request,
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
):
    """Get error data for a specific component."""
    try:
        if component not in ["rule_engine", "cache", "llm", "clarifier"]:
            raise HTTPException(status_code=404, detail=f"Component '{component}' not found")

        monitoring_service = get_monitoring_service(request)
        error_data = monitoring_service.get_component_errors(component)
        return error_data
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get component errors: {str(e)}")

# Recovery and Fallback Endpoints
@router.get("/fallback/stats")
async def get_fallback_stats(
    request: Request,
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
):
    """Get fallback statistics."""
    try:
        monitoring_service = get_monitoring_service(request)
        fallback_data = monitoring_service.get_fallback_stats()
        return fallback_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get fallback stats: {str(e)}")

@router.get("/recovery/capabilities")
async def get_recovery_capabilities(
    request: Request,
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
):
    """Get recovery capabilities and statistics."""
    try:
        monitoring_service = get_monitoring_service(request)
        recovery_data = monitoring_service.get_recovery_capabilities()
        return recovery_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get recovery capabilities: {str(e)}")

# Component Debugging Endpoint
@router.post("/debug/component")
async def debug_component(
    debug_request: ComponentDebugRequest,
    request: Request,
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
):
    """Debug a specific component with a test message."""
    try:
        if debug_request.component not in ["rule_engine", "cache", "llm", "clarifier"]:
            raise HTTPException(status_code=404, detail=f"Component '{debug_request.component}' not found")

        monitoring_service = get_monitoring_service(request)
        debug_result = monitoring_service.debug_component(debug_request)
        return debug_result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to debug component: {str(e)}")
