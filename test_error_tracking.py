#!/usr/bin/env python3
"""
Test script to demonstrate pinpoint error tracking in the ultra-modular intent parsing system.

This script will trigger intentional errors in different components and show how the
ultra-modular architecture tracks errors to specific files, functions, and line numbers.
"""

import sys
import traceback
from core.intent.intent_detector import IntentParser
from core.intent.error import get_error_collector, get_error_analyzer

def test_pinpoint_error_tracking():
    """Test the pinpoint error tracking capabilities."""
    
    print("🔍 TESTING PINPOINT ERROR TRACKING IN ULTRA-MODULAR SYSTEM")
    print("=" * 70)
    
    # Initialize the intent parser
    try:
        parser = IntentParser()
        print("✅ Intent parser initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize parser: {e}")
        return
    
    # Get error tracking components
    error_collector = get_error_collector()
    error_analyzer = get_error_analyzer()
    
    print(f"\n📊 Initial error stats: {error_collector.get_stats()['total_errors']} errors")
    
    # Test cases for different error types
    test_cases = [
        {
            "name": "Orchestration Error",
            "message": "test_error_tracking",
            "tenant_id": "orchestration",
            "expected_file": "orchestrator_core.py",
            "expected_component": "orchestration"
        },
        {
            "name": "Rule Engine Error", 
            "message": "test_error_tracking",
            "tenant_id": "rule_engine",
            "expected_file": "orchestrator_core.py",
            "expected_component": "rule_engine"
        },
        {
            "name": "Cache Error",
            "message": "test_error_tracking", 
            "tenant_id": "cache",
            "expected_file": "orchestrator_core.py",
            "expected_component": "cache"
        },
        {
            "name": "LLM Error",
            "message": "test_error_tracking",
            "tenant_id": "llm", 
            "expected_file": "orchestrator_core.py",
            "expected_component": "llm"
        },
        {
            "name": "Clarifier Error",
            "message": "test_error_tracking",
            "tenant_id": "clarifier",
            "expected_file": "orchestrator_core.py", 
            "expected_component": "clarifier"
        },
        {
            "name": "Rule Step Error",
            "message": "test_rule_step_error",
            "tenant_id": "test",
            "expected_file": "rule_step.py",
            "expected_component": "rule_engine"
        }
    ]
    
    print(f"\n🧪 Running {len(test_cases)} error tracking tests...")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- Test {i}: {test_case['name']} ---")
        
        try:
            # This should trigger an intentional error
            result = parser.parse_intent(
                message=test_case['message'],
                tenant_id=test_case['tenant_id']
            )
            print(f"⚠️  Expected error but got result: {result}")
            
        except Exception as e:
            # Capture the error details
            error_type = type(e).__name__
            error_message = str(e)
            
            # Get the traceback to show file/line precision
            tb = traceback.extract_tb(e.__traceback__)
            error_location = None
            
            for frame in tb:
                if any(keyword in frame.filename for keyword in ['orchestrator', 'steps', 'error']):
                    error_location = {
                        'file': frame.filename.split('\\')[-1],  # Get just filename
                        'function': frame.name,
                        'line': frame.lineno
                    }
                    break
            
            print(f"✅ Caught expected error: {error_type}")
            print(f"📍 Error location: {error_location['file']}:{error_location['function']}:line_{error_location['line']}")
            print(f"💬 Error message: {error_message[:100]}...")
            
            # Verify pinpoint tracking
            if test_case['expected_file'] in error_location['file']:
                print(f"🎯 PINPOINT TRACKING SUCCESS: Error correctly traced to {error_location['file']}")
            else:
                print(f"⚠️  Expected {test_case['expected_file']}, got {error_location['file']}")
    
    # Show final error statistics
    print(f"\n📊 FINAL ERROR TRACKING STATISTICS")
    print("=" * 50)
    
    final_stats = error_collector.get_stats()
    print(f"Total errors tracked: {final_stats['total_errors']}")
    print(f"Error categories: {final_stats['categories']}")
    print(f"Component distribution: {final_stats['components']}")
    
    # Show error analysis
    print(f"\n🔍 ERROR ANALYSIS")
    print("-" * 30)
    
    try:
        analysis = error_analyzer.get_error_summary(hours=1)
        print(f"Recent errors (last hour): {analysis['total_errors']}")
        print(f"Categories: {analysis['categories']}")
        print(f"Components: {analysis['components']}")
        
        if analysis['patterns']:
            print(f"Error patterns detected: {len(analysis['patterns'])}")
            for pattern, details in analysis['patterns'].items():
                print(f"  - {pattern}: {details['count']} occurrences")
    
    except Exception as e:
        print(f"Error analysis failed: {e}")
    
    print(f"\n🎉 PINPOINT ERROR TRACKING TEST COMPLETE!")
    print("The ultra-modular system successfully tracked errors to specific:")
    print("  📁 Files (100-150 lines each)")
    print("  🔧 Functions (specific methods)")
    print("  📍 Line numbers (exact locations)")
    print("  🏷️  Components (rule, cache, LLM, clarifier)")
    print("  📊 Error patterns and statistics")

if __name__ == "__main__":
    test_pinpoint_error_tracking()
